#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء النسخة النهائية الكاملة من رواية ريشة عنقاء الأخيرة
مع جميع التحسينات والإضافات النهائية
"""

import os
import re
from pathlib import Path
from datetime import datetime

def create_final_complete_novel():
    """إنشاء النسخة النهائية الكاملة من الرواية"""
    
    print("🚀 بدء إنشاء النسخة النهائية الكاملة...")
    
    # قراءة الملف الأساسي
    try:
        with open("ريشة_عنقاء_الأخيرة_الرواية_الكاملة_الأسطورية.md", 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ الملف الأساسي غير موجود")
        return False
    
    # إضافة التحسينات النهائية
    enhanced_content = add_final_enhancements(content)
    
    # إنشاء ملف HTML منسق
    html_content = create_html_version(enhanced_content)
    
    # إنشاء ملف Word جاهز للطباعة
    word_content = create_word_version(enhanced_content)
    
    # حفظ النسخ المختلفة
    files_created = []
    
    # النسخة النهائية Markdown
    final_md = "ريشة_عنقاء_الأخيرة_النسخة_النهائية_الكاملة_الأسطورية.md"
    with open(final_md, 'w', encoding='utf-8') as f:
        f.write(enhanced_content)
    files_created.append(final_md)
    
    # النسخة HTML
    final_html = "ريشة_عنقاء_الأخيرة_النسخة_النهائية_الكاملة.html"
    with open(final_html, 'w', encoding='utf-8') as f:
        f.write(html_content)
    files_created.append(final_html)
    
    # النسخة Word
    final_word = "ريشة_عنقاء_الأخيرة_النسخة_النهائية_الكاملة.md"
    with open(final_word, 'w', encoding='utf-8') as f:
        f.write(word_content)
    files_created.append(final_word)
    
    # إحصائيات نهائية
    stats = calculate_final_stats(enhanced_content)
    
    print("✅ تم إنشاء النسخة النهائية الكاملة بنجاح!")
    print(f"📁 الملفات المنشأة: {len(files_created)}")
    for file in files_created:
        print(f"   📄 {file}")
    
    print(f"\n📊 الإحصائيات النهائية:")
    for key, value in stats.items():
        print(f"   {key}: {value:,}")
    
    return True

def add_final_enhancements(content):
    """إضافة التحسينات النهائية للمحتوى"""
    
    # إضافة فهرس تفاعلي محسن
    enhanced_content = add_enhanced_index(content)
    
    # إضافة ملاحظات هامشية
    enhanced_content = add_footnotes(enhanced_content)
    
    # إضافة قاموس المصطلحات
    enhanced_content = add_glossary(enhanced_content)
    
    # إضافة مراجع وببليوغرافيا
    enhanced_content = add_bibliography(enhanced_content)
    
    return enhanced_content

def add_enhanced_index(content):
    """إضافة فهرس تفاعلي محسن"""
    
    index_section = """
## 📚 الفهرس التفاعلي المحسن 📚

### 🎯 دليل القراءة السريع
- **للقارئ الجديد:** ابدأ من الإهداء والمقدمة
- **للباحث عن الفلسفة:** ركز على الفصول 2، 6، 13، 18
- **لمحب المغامرة:** اقرأ الفصول 4، 9، 15، 17
- **للمهتم بالأساطير:** الفصول 5، 7، 8، 10

### 📖 خريطة الشخصيات
- **عنتر:** البطل الفيلسوف المحارب (الصفحات 11-200)
- **صفاء:** أميرة أطلانتيس وحارسة الأسرار (الصفحات 41-95)
- **أفلاطون:** الفيلسوف اليوناني العظيم (الصفحات 47-55)
- **زيوس الأسود:** الشرير المأساوي (الصفحات 53-95)
- **هرمس:** حكيم الحكماء (الصفحات 79-99)

### 🗺️ خريطة العوالم
- **طنجة:** العالم الواقعي (الصفحات 11-44، 195-200)
- **مملكة القمر العميق:** العالم السحري (الصفحات 41-100)
- **أثينا القديمة:** العالم التاريخي (الصفحات 45-56)
- **أطلانتيس:** العالم الأسطوري (الصفحات 52-95)

---
"""
    
    # إدراج الفهرس بعد المقدمة
    content = content.replace("---\n\n## ❖ فهرس المحتويات الكامل ❖", 
                             index_section + "\n## ❖ فهرس المحتويات الكامل ❖")
    
    return content

def add_footnotes(content):
    """إضافة ملاحظات هامشية"""
    
    footnotes = """
---

## 📝 الملاحظات الهامشية

### الفصل الأول - يقظة في طنجة
¹ طنجة: مدينة مغربية تقع على مضيق جبل طارق، نقطة التقاء أفريقيا وأوروبا
² القصبة: الحي القديم المحصن في المدن المغربية التقليدية

### الفصل السادس - لقاء مع أفلاطون  
³ أفلاطون (428-348 ق.م): فيلسوف يوناني، تلميذ سقراط ومعلم أرسطو
⁴ الأكاديمية: المدرسة الفلسفية التي أسسها أفلاطون في أثينا عام 387 ق.م

### الفصل السابع - أسرار أطلانتيس
⁵ أطلانتيس: الحضارة الأسطورية التي ذكرها أفلاطون في محاوراته
⁶ قلب العنقاء: رمز أسطوري لمصدر الطاقة الكونية والحكمة

### الفصل الثالث عشر - لغز النجمة السباعية
⁷ النجمة السباعية: رمز الأبعاد السبعة للوجود في الفلسفة الباطنية
⁸ الأبعاد السبعة: المادي، العاطفي، العقلي، الزمني، الروحي، الكوني، الإلهي

---
"""
    
    content += footnotes
    return content

def add_glossary(content):
    """إضافة قاموس المصطلحات"""
    
    glossary = """
## 📖 قاموس المصطلحات والمفاهيم

### أ
- **أطلانتيس:** الحضارة الأسطورية المتقدمة التي سقطت بسبب الكبرياء والجشع
- **الأبعاد السبعة:** مستويات الوجود المختلفة من المادي إلى الإلهي
- **الأرواح السبع:** الأرواح الكونية التي تحمل جوانب الحقيقة المطلقة

### ت
- **تجسد:** ولادة الروح في جسد مادي عبر دورات زمنية مختلفة
- **التوازن الكوني:** الحالة المثلى للكون عندما تتوحد القوى المختلفة

### ح
- **حارس الأسرار:** من يحمي المعرفة المقدسة من سوء الاستخدام
- **الحكمة الأزلية:** المعرفة الكونية التي تتجاوز الزمان والمكان

### ر
- **ريشة العنقاء:** رمز القوة الروحية والتجدد والخلود
- **روح الحكمة الأزلية:** الروح التي تبحث عن الحقيقة عبر التجسدات

### ع
- **عنقاء:** طائر أسطوري يرمز للتجدد والخلود والقوة الروحية
- **العالم الموازي:** أبعاد أخرى من الوجود تتداخل مع عالمنا

### ق
- **قلب العنقاء:** مصدر الطاقة الكونية والحكمة في أطلانتيس
- **القوة المتوازنة:** استخدام القوة بحكمة وحب وليس بجشع

### م
- **مملكة القمر العميق:** البُعد الذي تُحفظ فيه أسرار الحضارات المندثرة
- **المختار:** الشخص المقدر له حماية التوازن الكوني
- **مكتبة الأسرار المفقودة:** مكان حفظ كل المعرفة المفقودة في التاريخ

### ن
- **النجمة السباعية:** رمز الأبعاد السبعة للوجود والكمال الروحي
- **النبوءة:** التنبؤ بالأحداث المستقبلية المهمة للكون

---
"""
    
    content += glossary
    return content

def add_bibliography(content):
    """إضافة مراجع وببليوغرافيا"""
    
    bibliography = """
## 📚 المراجع والمصادر

### المصادر الفلسفية
- أفلاطون: "الجمهورية" و"محاورة تيماوس" (مصدر قصة أطلانتيس)
- أرسطو: "الميتافيزيقا" و"الأخلاق النيقوماخية"
- ابن سينا: "الشفاء" و"الإشارات والتنبيهات"
- ابن رشد: "تهافت التهافت" و"فصل المقال"
- الرومي: "المثنوي" و"ديوان شمس التبريزي"

### المصادر التاريخية والأسطورية
- هرمس تريسميجيستوس: "الكتاب الزمردي" (النصوص الهرمسية)
- الأساطير اليونانية والرومانية
- التراث الإسلامي والصوفي
- الأساطير المغربية والأمازيغية

### المصادر العلمية المعاصرة
- فيزياء الكم ونظرية الأوتار
- علم الكونيات والفلك الحديث
- علم النفس التحليلي (كارل يونغ)
- الفلسفة المعاصرة وفلسفة العقل

### الإلهام الأدبي
- الأدب العربي الكلاسيكي والمعاصر
- أدب الخيال العلمي والفانتازيا العالمي
- الشعر الصوفي والفلسفي
- الأدب المغربي المعاصر

---

## 🎯 رسالة شكر وتقدير

### إلى القراء
شكراً لكل من رافقني في هذه الرحلة الأسطورية عبر عوالم الخيال والفلسفة. أتمنى أن تكون هذه الرواية قد أضافت شيئاً جميلاً إلى حياتكم.

### إلى المعلمين والحكماء
شكراً لكل من علمني حرفاً أو فكرة أو حكمة. من أساتذة الجامعة إلى الكتب القديمة، من الأصدقاء إلى التجارب الحياتية.

### إلى مدينة طنجة
شكراً لمدينتي الحبيبة التي ألهمتني وعلمتني أن الجمال يكمن في التنوع والانفتاح على العالم.

### إلى العائلة والأصدقاء
شكراً لكل من دعمني وآمن بحلمي في أن أصبح كاتباً. حبكم هو الذي جعل هذا العمل ممكناً.

---

**"الحكمة ليست في امتلاك الإجابات، بل في طرح الأسئلة الصحيحة."**  
*- عنترة، إمبراطور الكلمات*

---

**© جميع الحقوق محفوظة - محمد سعيد بوزرهون (عنترة) - 2024**  
**Moxakis MX / Lens Dragon Productions**  
**الطبعة الأولى - النسخة الكاملة النهائية**

---

*تم الانتهاء من كتابة هذه الرواية في ديسمبر 2024 في مدينة طنجة، المغرب*
"""
    
    content += bibliography
    return content

def create_html_version(content):
    """إنشاء نسخة HTML منسقة"""
    
    html_template = f"""<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ريشة عنقاء الأخيرة - الرواية الكاملة</title>
    <style>
        body {{
            font-family: 'Amiri', 'Times New Roman', serif;
            line-height: 1.8;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #d4af37;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }}
        h2 {{
            color: #8b4513;
            border-bottom: 2px solid #d4af37;
            padding-bottom: 10px;
            margin-top: 40px;
        }}
        h3 {{
            color: #2c3e50;
            margin-top: 30px;
        }}
        .page-number {{
            text-align: center;
            font-weight: bold;
            color: #d4af37;
            margin: 20px 0;
            font-size: 1.2em;
        }}
        .image-description {{
            background: #f8f9fa;
            border-left: 4px solid #d4af37;
            padding: 15px;
            margin: 20px 0;
            font-style: italic;
            border-radius: 5px;
        }}
        .quote {{
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            font-style: italic;
            border-radius: 5px;
        }}
        .author-info {{
            background: #f0f0f0;
            padding: 20px;
            border-radius: 10px;
            margin-top: 40px;
            text-align: center;
        }}
        @media print {{
            body {{ background: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        {content}
    </div>
</body>
</html>"""
    
    return html_template

def create_word_version(content):
    """إنشاء نسخة جاهزة لـ Word"""
    
    # تنسيق خاص لـ Word
    word_content = content.replace("**", "")  # إزالة التنسيق الجريء
    word_content = word_content.replace("*", "")  # إزالة التنسيق المائل
    
    # إضافة فواصل الصفحات
    word_content = word_content.replace("---", "\n\n[فاصل صفحة]\n\n")
    
    return word_content

def calculate_final_stats(content):
    """حساب الإحصائيات النهائية"""
    
    lines = len(content.split('\n'))
    words = len(content.split())
    characters = len(content)
    pages = words // 250  # تقدير 250 كلمة لكل صفحة
    
    # عد الفصول
    chapters = len(re.findall(r'## .* الفصل', content))
    
    # عد الصور
    images = len(re.findall(r'\[صفحة مخصصة للصورة التعبيرية\]', content))
    
    return {
        "📝 عدد الأسطر": lines,
        "📖 عدد الكلمات": words,
        "🔤 عدد الأحرف": characters,
        "📄 عدد الصفحات المقدر": pages,
        "📚 عدد الفصول": chapters,
        "🖼️ عدد الصور التعبيرية": images
    }

if __name__ == "__main__":
    print("🎯 إنشاء النسخة النهائية الكاملة من الرواية")
    print("=" * 60)
    
    success = create_final_complete_novel()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 تم إكمال النسخة النهائية بنجاح!")
        print("📚 الرواية جاهزة للنشر والتوزيع")
        print("🌟 رحلة ملحمية من 200+ صفحة مكتملة!")
    else:
        print("\n" + "=" * 60)
        print("❌ فشل في إنشاء النسخة النهائية")
