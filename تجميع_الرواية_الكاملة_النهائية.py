#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تجميع الرواية الكاملة النهائية - ريشة عنقاء الأخيرة
تأليف: محمد سعيد بوزرهون (عنترة)
"""

import os
import re
from pathlib import Path

def read_file_safely(filename):
    """قراءة محتوى الملف مع معالجة الأخطاء"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"⚠️ الملف غير موجود: {filename}")
        return ""
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف {filename}: {e}")
        return ""

def create_complete_novel():
    """إنشاء الرواية الكاملة النهائية"""
    
    print("🚀 بدء تجميع الرواية الكاملة...")
    
    # قراءة الملفات المختلفة
    files_to_read = [
        "ريشة_عنقاء_الأخيرة_النسخة_الكاملة_الموحدة.md",
        "ريشة_عنقاء_الأخيرة_الجزء_الثاني.md", 
        "ريشة_عنقاء_الأخيرة_الجزء_الثالث.md",
        "صفحة_تعريف_المؤلف_الاحترافية.md"
    ]
    
    contents = {}
    for filename in files_to_read:
        print(f"📖 قراءة ملف: {filename}")
        contents[filename] = read_file_safely(filename)
    
    # بناء الرواية الكاملة
    novel_parts = []
    
    # العنوان والمقدمة
    novel_parts.append("# ❖ ريشة عنقاء الأخيرة ❖\n")
    novel_parts.append("**رواية فانتازيا فلسفية ملحمية - 200 صفحة**\n")
    novel_parts.append("*تأليف: محمد سعيد بوزرهون (عنترة)*")
    novel_parts.append("*شركة: Moxakis MX / Lens Dragon*")
    novel_parts.append("*البريد: <EMAIL> | الهاتف: +212 626 568 075*")
    novel_parts.append("*المدينة الرمزية: أطلانتس – طنجة*\n")
    novel_parts.append("⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺ ⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺\n")
    novel_parts.append("---\n")
    
    # إضافة المحتوى الرئيسي
    main_content = contents.get("ريشة_عنقاء_الأخيرة_النسخة_الكاملة_الموحدة.md", "")
    if main_content:
        # استخراج الجزء الأساسي من المحتوى
        lines = main_content.split('\n')
        start_adding = False
        for line in lines:
            if "فهرس المحتويات" in line:
                start_adding = True
            if start_adding:
                novel_parts.append(line)
    
    # إضافة الأجزاء الإضافية
    for part_file in ["ريشة_عنقاء_الأخيرة_الجزء_الثاني.md", "ريشة_عنقاء_الأخيرة_الجزء_الثالث.md"]:
        content = contents.get(part_file, "")
        if content:
            novel_parts.append(f"\n---\n## 📚 المحتوى من: {part_file}\n")
            # تنظيف المحتوى
            cleaned_content = re.sub(r'^#.*?❖.*?❖.*?\n', '', content, flags=re.MULTILINE)
            novel_parts.append(cleaned_content)
    
    # إضافة صفحة المؤلف
    author_content = contents.get("صفحة_تعريف_المؤلف_الاحترافية.md", "")
    if author_content:
        novel_parts.append("\n---\n## 📚 عن المؤلف\n")
        novel_parts.append(author_content)
    
    # حفظ الرواية الكاملة
    output_file = "ريشة_عنقاء_الأخيرة_الرواية_الكاملة_النهائية.md"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(novel_parts))
        
        print(f"✅ تم إنشاء الرواية الكاملة بنجاح: {output_file}")
        
        # إحصائيات
        total_text = '\n'.join(novel_parts)
        word_count = len(total_text.split())
        line_count = len(novel_parts)
        estimated_pages = word_count // 250  # تقدير 250 كلمة لكل صفحة
        
        print(f"📊 إحصائيات الرواية:")
        print(f"   📝 عدد الكلمات: {word_count:,}")
        print(f"   📄 عدد الأسطر: {line_count:,}")
        print(f"   📖 عدد الصفحات المقدر: {estimated_pages}")
        
        if estimated_pages >= 200:
            print("🎉 الرواية تحتوي على أكثر من 200 صفحة كما هو مطلوب!")
        else:
            print(f"⚠️ الرواية تحتاج إلى {200 - estimated_pages} صفحة إضافية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ الملف: {e}")
        return False

def create_word_document():
    """إنشاء مستند Word من الرواية"""
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        
        print("📄 إنشاء مستند Word...")
        
        # قراءة الرواية المكتملة
        with open("ريشة_عنقاء_الأخيرة_الرواية_الكاملة_النهائية.md", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إنشاء مستند Word
        doc = Document()
        
        # إعداد الصفحة
        section = doc.sections[0]
        section.page_height = Inches(11)
        section.page_width = Inches(8.5)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        
        # معالجة المحتوى
        lines = content.split('\n')
        page_count = 1
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # العناوين الرئيسية
            if line.startswith('# ❖'):
                if page_count > 1:
                    doc.add_page_break()
                heading = doc.add_heading(line.replace('# ❖', '').replace('❖', '').strip(), 0)
                heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
            # العناوين الفرعية
            elif line.startswith('## ❖') or line.startswith('## ⟐'):
                heading = doc.add_heading(line.replace('##', '').replace('❖', '').replace('⟐', '').strip(), 1)
                heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
            # العناوين الصغيرة
            elif line.startswith('### '):
                heading = doc.add_heading(line.replace('###', '').strip(), 2)
                
            # النص العادي
            elif line and not line.startswith('*[') and not line.startswith('**وصف الصورة'):
                # تنظيف النص
                clean_line = re.sub(r'[⟐⟡⟟✦✵⬖⬗⬘⬙✺❖]+', '', line).strip()
                
                if clean_line:
                    if 'ترقيم الصفحة' in clean_line:
                        # إضافة رقم الصفحة
                        page_para = doc.add_paragraph(clean_line)
                        page_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        page_count += 1
                        if page_count % 2 == 0:
                            doc.add_page_break()
                    else:
                        # إضافة النص العادي
                        para = doc.add_paragraph(clean_line)
                        para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # حفظ المستند
        word_filename = "ريشة_عنقاء_الأخيرة_رواية_كاملة_نهائية.docx"
        doc.save(word_filename)
        print(f"✅ تم إنشاء مستند Word: {word_filename}")
        
        return True
        
    except ImportError:
        print("⚠️ مكتبة python-docx غير مثبتة. تخطي إنشاء مستند Word.")
        return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء مستند Word: {e}")
        return False

if __name__ == "__main__":
    print("🎯 تجميع الرواية الكاملة النهائية")
    print("=" * 50)
    
    # إنشاء الرواية المكتملة
    success = create_complete_novel()
    
    if success:
        print("\n📄 محاولة إنشاء مستند Word...")
        create_word_document()
        
        print("\n" + "=" * 50)
        print("🎉 تم إكمال العملية بنجاح!")
        print("📚 الرواية جاهزة للمراجعة والنشر")
        print("📁 الملفات المنشأة:")
        print("   - ريشة_عنقاء_الأخيرة_الرواية_الكاملة_النهائية.md")
        print("   - ريشة_عنقاء_الأخيرة_رواية_كاملة_نهائية.docx (إن أمكن)")
    else:
        print("\n" + "=" * 50)
        print("❌ فشل في تجميع الرواية")
