#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء الرواية الكاملة النهائية - ريشة عنقاء الأخيرة
تأليف: محمد سعيد بوزرهون (عنترة)
"""

import os
import re
from pathlib import Path

def read_file_content(filename):
    """قراءة محتوى الملف مع معالجة الترميز"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"الملف غير موجود: {filename}")
        return ""
    except Exception as e:
        print(f"خطأ في قراءة الملف {filename}: {e}")
        return ""

def clean_text(text):
    """تنظيف النص من الرموز الزائدة والتنسيق"""
    # إزالة الرموز الزخرفية الزائدة
    text = re.sub(r'[⟐⟡⟟✦✵⬖⬗⬘⬙✺❖]+', '', text)
    
    # تنظيف العناوين
    text = re.sub(r'^#+\s*', '', text, flags=re.MULTILINE)
    
    # إزالة الأسطر الفارغة المتكررة
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
    
    # تنظيف أوصاف الصور
    text = re.sub(r'\*\[صفحة مخصصة للصورة التعبيرية\]\*.*?(?=\n\n|\n###|\n##|\Z)', '', text, flags=re.DOTALL)
    
    return text.strip()

def create_complete_novel():
    """إنشاء الرواية الكاملة"""
    
    # قراءة الملفات المختلفة
    main_file = "ريشة_عنقاء_الأخيرة_النسخة_الكاملة_الموحدة.md"
    part2_file = "ريشة_عنقاء_الأخيرة_الجزء_الثاني.md"
    part3_file = "ريشة_عنقاء_الأخيرة_الجزء_الثالث.md"
    author_file = "صفحة_تعريف_المؤلف_الاحترافية.md"
    
    print("جاري قراءة الملفات...")
    
    main_content = read_file_content(main_file)
    part2_content = read_file_content(part2_file)
    part3_content = read_file_content(part3_file)
    author_content = read_file_content(author_file)
    
    # بناء الرواية الكاملة
    complete_novel = []
    
    # العنوان والمقدمة
    complete_novel.append("# ❖ ريشة عنقاء الأخيرة ❖")
    complete_novel.append("")
    complete_novel.append("**رواية فانتازيا فلسفية ملحمية - 200 صفحة**")
    complete_novel.append("")
    complete_novel.append("*تأليف: محمد سعيد بوزرهون (عنترة)*")
    complete_novel.append("*شركة: Moxakis MX / Lens Dragon*")
    complete_novel.append("*البريد: <EMAIL> | الهاتف: +212 626 568 075*")
    complete_novel.append("*المدينة الرمزية: أطلانتس – طنجة*")
    complete_novel.append("")
    complete_novel.append("⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺ ⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺")
    complete_novel.append("")
    complete_novel.append("---")
    complete_novel.append("")
    
    # فهرس المحتويات
    complete_novel.append("## ❖ فهرس المحتويات الكامل ❖")
    complete_novel.append("")
    complete_novel.append("### ⟐ القسم الأول: البدايات ⟐")
    complete_novel.append("**الإهداء والمقدمة** ................................. صفحة 3")
    complete_novel.append("**الفصل التمهيدي:** المقدمة الفلسفية .................... صفحة 7")
    complete_novel.append("**الفصل الأول:** يقظة في طنجة ......................... صفحة 15")
    complete_novel.append("**الفصل الثاني:** رحلة بوسيدون عبر البحار .............. صفحة 25")
    complete_novel.append("**الفصل الثالث:** أسرار جزيرة السماء .................. صفحة 35")
    complete_novel.append("")
    complete_novel.append("### ⟡ القسم الثاني: الأساطير الكبرى ⟡")
    complete_novel.append("**الفصل الرابع:** سقوط أطلانتيس ...................... صفحة 45")
    complete_novel.append("**الفصل الخامس:** أفلاطون والأسطورة .................. صفحة 55")
    complete_novel.append("**الفصل السادس:** الحقيقة والخيال ..................... صفحة 65")
    complete_novel.append("**الفصل السابع:** رسالة الإمبراطور عنترة ............... صفحة 75")
    complete_novel.append("")
    complete_novel.append("### ⟟ القسم الثالث: مملكة القمر العميق ⟟")
    complete_novel.append("**الفصل الثامن:** بوابة الضباب ........................ صفحة 85")
    complete_novel.append("**الفصل التاسع:** صفاء، حارسة الأسرار ................. صفحة 95")
    complete_novel.append("**الفصل العاشر:** برج الرمال المتحركة ................. صفحة 105")
    complete_novel.append("**الفصل الحادي عشر:** ظل الطفولة ..................... صفحة 115")
    complete_novel.append("**الفصل الثاني عشر:** لغز النجمة السباعية .............. صفحة 125")
    complete_novel.append("**الفصل الثالث عشر:** الكتاب المتكلم .................. صفحة 135")
    complete_novel.append("**الفصل الرابع عشر:** خيانة في قلب الحرس ............. صفحة 145")
    complete_novel.append("**الفصل الخامس عشر:** تضحية صفاء .................... صفحة 155")
    complete_novel.append("**الفصل السادس عشر:** عودة الإمبراطور ................ صفحة 165")
    complete_novel.append("")
    complete_novel.append("### ✦ القسم الرابع: الخاتمة والأسرار ✦")
    complete_novel.append("**الفصل السابع عشر:** كشف الحقيقة الكاملة ............ صفحة 175")
    complete_novel.append("**الخاتمة الكبرى:** رسالة الإمبراطور الأخيرة ........... صفحة 185")
    complete_novel.append("**عن المؤلف:** عنترة - إمبراطور الكلمات .............. صفحة 195")
    complete_novel.append("")
    complete_novel.append("---")
    complete_novel.append("")
    
    # إضافة المحتوى الرئيسي
    if main_content:
        # استخراج المحتوى الأساسي
        lines = main_content.split('\n')
        in_content = False
        for line in lines:
            if "الإهداء والمقدمة" in line:
                in_content = True
            if in_content:
                complete_novel.append(line)
    
    # إضافة الأجزاء الإضافية
    if part2_content:
        complete_novel.append("\n---\n")
        complete_novel.append("## 📚 المحتوى من الجزء الثاني")
        complete_novel.append("")
        complete_novel.extend(clean_text(part2_content).split('\n'))
    
    if part3_content:
        complete_novel.append("\n---\n")
        complete_novel.append("## 📚 المحتوى من الجزء الثالث")
        complete_novel.append("")
        complete_novel.extend(clean_text(part3_content).split('\n'))
    
    # إضافة صفحة المؤلف
    if author_content:
        complete_novel.append("\n---\n")
        complete_novel.append("## 📚 عن المؤلف")
        complete_novel.append("")
        complete_novel.extend(clean_text(author_content).split('\n'))
    
    # حفظ الرواية الكاملة
    output_file = "ريشة_عنقاء_الأخيرة_الرواية_الكاملة_النهائية.md"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(complete_novel))
        
        print(f"✅ تم إنشاء الرواية الكاملة بنجاح: {output_file}")
        print(f"📊 عدد الأسطر: {len(complete_novel)}")
        
        # إحصائيات
        word_count = len(' '.join(complete_novel).split())
        print(f"📝 عدد الكلمات التقريبي: {word_count}")
        print(f"📄 عدد الصفحات التقريبي: {word_count // 250}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في حفظ الملف: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إنشاء الرواية الكاملة النهائية...")
    print("=" * 50)
    
    success = create_complete_novel()
    
    if success:
        print("=" * 50)
        print("✅ تم إكمال العملية بنجاح!")
        print("📚 الرواية جاهزة للمراجعة والنشر")
    else:
        print("=" * 50)
        print("❌ فشل في إنشاء الرواية")
